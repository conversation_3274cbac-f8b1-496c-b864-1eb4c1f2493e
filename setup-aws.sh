#!/bin/bash

# CFOx AWS Setup Script
# This script helps configure AWS credentials and verify S3/CloudFront access

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUCKET_NAME="cfox"
REGION="ap-south-1"
CLOUDFRONT_DISTRIBUTION_ID="EAHXVWDND4JQE"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if AWS CLI is installed
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Installing..."
        
        # Try to install AWS CLI
        if command -v brew &> /dev/null; then
            print_status "Installing AWS CLI with Homebrew..."
            brew install awscli
        elif command -v pip3 &> /dev/null; then
            print_status "Installing AWS CLI with pip3..."
            pip3 install awscli --user
        elif command -v pip &> /dev/null; then
            print_status "Installing AWS CLI with pip..."
            pip install awscli --user
        else
            print_error "Could not install AWS CLI automatically. Please install it manually:"
            echo "  https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
            exit 1
        fi
    fi
    print_success "AWS CLI is installed"
}

# Configure AWS credentials
configure_aws() {
    print_status "Configuring AWS credentials..."
    
    # Check if credentials are already configured
    if aws sts get-caller-identity &> /dev/null; then
        print_success "AWS credentials are already configured"
        aws sts get-caller-identity
        return 0
    fi
    
    echo ""
    echo "Please enter your AWS credentials:"
    echo "You can find these in your AWS IAM console under 'Security credentials'"
    echo ""
    
    read -p "AWS Access Key ID: " access_key
    read -s -p "AWS Secret Access Key: " secret_key
    echo ""
    
    # Configure AWS CLI
    aws configure set aws_access_key_id "$access_key"
    aws configure set aws_secret_access_key "$secret_key"
    aws configure set default.region "$REGION"
    aws configure set default.output "json"
    
    # Verify credentials
    if aws sts get-caller-identity &> /dev/null; then
        print_success "AWS credentials configured successfully"
        aws sts get-caller-identity
    else
        print_error "Failed to configure AWS credentials. Please check your keys."
        exit 1
    fi
}

# Verify S3 bucket access
verify_s3_access() {
    print_status "Verifying S3 bucket access..."
    
    if aws s3 ls "s3://$BUCKET_NAME" &> /dev/null; then
        print_success "S3 bucket '$BUCKET_NAME' is accessible"
        
        # List some objects to verify read access
        object_count=$(aws s3 ls "s3://$BUCKET_NAME" --recursive | wc -l)
        print_status "Found $object_count objects in bucket"
        
    else
        print_warning "S3 bucket '$BUCKET_NAME' is not accessible"
        print_status "Attempting to create bucket..."
        
        if aws s3 mb "s3://$BUCKET_NAME" --region "$REGION"; then
            print_success "S3 bucket created successfully"
        else
            print_error "Failed to create S3 bucket. Please check your permissions."
            exit 1
        fi
    fi
}

# Verify CloudFront access
verify_cloudfront_access() {
    print_status "Verifying CloudFront distribution access..."
    
    if aws cloudfront get-distribution --id "$CLOUDFRONT_DISTRIBUTION_ID" &> /dev/null; then
        print_success "CloudFront distribution '$CLOUDFRONT_DISTRIBUTION_ID' is accessible"
        
        # Get distribution status
        status=$(aws cloudfront get-distribution --id "$CLOUDFRONT_DISTRIBUTION_ID" --query 'Distribution.Status' --output text)
        domain=$(aws cloudfront get-distribution --id "$CLOUDFRONT_DISTRIBUTION_ID" --query 'Distribution.DomainName' --output text)
        
        print_status "Distribution Status: $status"
        print_status "Distribution Domain: $domain"
        
    else
        print_error "CloudFront distribution '$CLOUDFRONT_DISTRIBUTION_ID' is not accessible"
        print_warning "Please verify the distribution ID and your permissions"
        exit 1
    fi
}

# Test deployment permissions
test_permissions() {
    print_status "Testing deployment permissions..."
    
    # Test S3 write permissions
    test_file="test-deployment-$(date +%s).txt"
    echo "Test deployment file" > "$test_file"
    
    if aws s3 cp "$test_file" "s3://$BUCKET_NAME/$test_file"; then
        print_success "S3 write permission verified"
        
        # Clean up test file
        aws s3 rm "s3://$BUCKET_NAME/$test_file"
        rm "$test_file"
    else
        print_error "S3 write permission failed"
        rm "$test_file"
        exit 1
    fi
    
    # Test CloudFront invalidation permissions
    if aws cloudfront create-invalidation --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" --paths "/test-path" &> /dev/null; then
        print_success "CloudFront invalidation permission verified"
    else
        print_warning "CloudFront invalidation permission failed - deployments will work but cache won't be cleared"
    fi
}

# Display summary
display_summary() {
    print_success "AWS setup complete!"
    echo ""
    echo "📋 Configuration Summary:"
    echo "========================="
    echo "🪣 S3 Bucket: $BUCKET_NAME"
    echo "🌍 Region: $REGION"
    echo "☁️  CloudFront Distribution: $CLOUDFRONT_DISTRIBUTION_ID"
    echo ""
    echo "🚀 Ready to deploy! Run:"
    echo "   ./deploy-to-s3.sh"
    echo ""
}

# Main setup function
main() {
    echo "⚙️  CFOx AWS Setup Script"
    echo "========================="
    echo ""
    
    check_aws_cli
    configure_aws
    verify_s3_access
    verify_cloudfront_access
    test_permissions
    display_summary
}

# Run main function
main "$@"
