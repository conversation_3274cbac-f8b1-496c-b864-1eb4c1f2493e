"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useThemeStore } from "@/stores/themeStore";
import { motion } from "framer-motion";
import { Moon, Palette, Sun } from "lucide-react";
import { useEffect, useState } from "react";

export default function ThemeToggle() {
  const { version, toggleTheme } = useThemeStore();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const handleToggle = () => {
    console.log('Toggle clicked, current version:', version);
    toggleTheme();
    console.log('After toggle, new version should be:', version === 'v1' ? 'v2' : 'v1');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.8 }}
      className="fixed bottom-6 right-6 z-50"
    >
      <div className="bg-slate-800/90 border border-slate-700 rounded-2xl p-4 shadow-lg">
        <div className="flex items-center gap-3 mb-3">
          <Palette className="w-5 h-5 text-slate-300" />
          <span className="text-sm font-medium text-slate-200">
            UI Version
          </span>
        </div>

        <div className="flex items-center gap-2 mb-3">
          <Badge
            variant="secondary"
            className="text-xs bg-slate-700 text-slate-200"
          >
            {version === 'v1' ? (
              <>
                <Moon className="w-3 h-3 mr-1" />
                V1 Dark
              </>
            ) : (
              <>
                <Sun className="w-3 h-3 mr-1" />
                V2 Light
              </>
            )}
          </Badge>
        </div>

        <Button
          onClick={handleToggle}
          size="sm"
          className="w-full text-xs bg-blue-600 hover:bg-blue-700 text-white"
        >
          Switch to {version === 'v1' ? 'V2 Light' : 'V1 Dark'}
        </Button>

        <p className="text-xs text-slate-400 mt-2 text-center">
          {version === 'v1' ? 'Current: Dark Theme' : 'New: Clean & Professional'}
        </p>
      </div>
    </motion.div>
  );
}
