"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useThemeStore } from "@/stores/themeStore";
import { motion } from "framer-motion";
import { Palette, <PERSON>, <PERSON> } from "lucide-react";

export default function ThemeToggle() {
  const { version, toggleTheme } = useThemeStore();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.8 }}
      className="fixed bottom-6 right-6 z-50"
    >
      <div className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-4 shadow-lg theme-v2:bg-white theme-v2:border-gray-200 theme-v1:bg-slate-800/90 theme-v1:border-slate-700">
        <div className="flex items-center gap-3 mb-3">
          <Palette className="w-5 h-5 text-gray-600 theme-v1:text-slate-300" />
          <span className="text-sm font-medium text-gray-700 theme-v1:text-slate-200">
            UI Version
          </span>
        </div>
        
        <div className="flex items-center gap-2 mb-3">
          <Badge 
            variant={version === 'v1' ? 'default' : 'secondary'}
            className="text-xs"
          >
            {version === 'v1' ? (
              <>
                <Moon className="w-3 h-3 mr-1" />
                V1 Dark
              </>
            ) : (
              <>
                <Sun className="w-3 h-3 mr-1" />
                V2 Light
              </>
            )}
          </Badge>
        </div>
        
        <Button
          onClick={toggleTheme}
          size="sm"
          className="w-full text-xs"
          variant={version === 'v1' ? 'outline' : 'default'}
        >
          Switch to {version === 'v1' ? 'V2 Light' : 'V1 Dark'}
        </Button>
        
        <p className="text-xs text-gray-500 theme-v1:text-slate-400 mt-2 text-center">
          {version === 'v1' ? 'Current: Dark Theme' : 'New: Clean & Professional'}
        </p>
      </div>
    </motion.div>
  );
}
