import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type ThemeVersion = 'v1' | 'v2';

interface ThemeState {
  version: ThemeVersion;
  setTheme: (version: ThemeVersion) => void;
  toggleTheme: () => void;
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      version: 'v1', // Default to V1 (current dark theme)
      
      setTheme: (version: ThemeVersion) => {
        set({ version });
        
        // Apply theme class to document body
        if (typeof window !== 'undefined') {
          const body = document.body;
          body.classList.remove('theme-v1', 'theme-v2');
          body.classList.add(`theme-${version}`);
          
          // Also apply light/dark class for existing CSS variables
          if (version === 'v2') {
            body.classList.add('light');
            body.classList.remove('dark');
          } else {
            body.classList.remove('light');
            body.classList.add('dark');
          }
        }
      },
      
      toggleTheme: () => {
        const currentVersion = get().version;
        const newVersion = currentVersion === 'v1' ? 'v2' : 'v1';
        get().setTheme(newVersion);
      },
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({ version: state.version }),
      onRehydrateStorage: () => (state) => {
        // Apply theme on page load
        if (state && typeof window !== 'undefined') {
          const body = document.body;
          body.classList.remove('theme-v1', 'theme-v2');
          body.classList.add(`theme-${state.version}`);
          
          if (state.version === 'v2') {
            body.classList.add('light');
            body.classList.remove('dark');
          } else {
            body.classList.remove('light');
            body.classList.add('dark');
          }
        }
      },
    }
  )
);
