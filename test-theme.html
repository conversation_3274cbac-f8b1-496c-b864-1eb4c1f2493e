<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CFOx V2 Theme Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* V1 Theme (Dark) - Default */
        .theme-v1 {
            --background: #0f172a;
            --foreground: #f8fafc;
            --card: #1e293b;
            --card-foreground: #f8fafc;
            --border: #334155;
        }

        /* V2 Theme (Light) - New Clean Design */
        .theme-v2 {
            --background: #ffffff;
            --foreground: #000000;
            --card: #ffffff;
            --card-foreground: #000000;
            --border: #e5e7eb;
        }

        .theme-v2 .v2-gradient-bg {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        }

        .theme-v2 .v2-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .theme-v2 .v2-header {
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
        }

        .theme-v2 .v2-text-primary {
            color: #000000;
        }

        .theme-v2 .v2-text-secondary {
            color: #6b7280;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            transition: background-color 0.3s ease, color 0.3s ease;
        }
    </style>
</head>
<body class="theme-v1">
    <div class="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 theme-v2:bg-white theme-v2:v2-gradient-bg">
        <!-- Header -->
        <header class="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50 theme-v2:v2-header theme-v2:bg-white/90 theme-v2:border-gray-200">
            <div class="container mx-auto px-4 py-4 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">CF</span>
                    </div>
                    <span class="text-xl font-bold text-white theme-v2:v2-text-primary">CFOx</span>
                </div>
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-slate-300 hover:text-white transition-colors theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary">For SMEs</a>
                    <a href="#" class="text-slate-300 hover:text-white transition-colors theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary">For Investors</a>
                    <button class="bg-white text-slate-900 hover:bg-slate-100 rounded-full px-6 py-2 theme-v2:bg-black theme-v2:text-white theme-v2:hover:bg-gray-800">
                        Sign up
                    </button>
                </nav>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="relative overflow-hidden py-20 px-4">
            <div class="relative max-w-7xl mx-auto">
                <div class="text-center mb-16">
                    <div class="mb-6 px-6 py-2 text-sm font-medium bg-blue-500/10 text-blue-300 border border-blue-500/20 rounded-full inline-block theme-v2:bg-blue-50 theme-v2:text-blue-600 theme-v2:border-blue-200">
                        🚀 10X Growth Hack Program Available
                    </div>
                    <h1 class="text-5xl sm:text-7xl font-bold text-white mb-8 leading-tight theme-v2:v2-text-primary">
                        Are you
                        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-emerald-400 theme-v2:text-blue-600 theme-v2:bg-none">
                            10x growth ready?
                        </span>
                    </h1>
                    <p class="text-xl text-slate-300 mb-12 max-w-2xl mx-auto leading-relaxed theme-v2:v2-text-secondary">
                        Data-driven financial evaluation platform that automates scoring, provides actionable insights, and connects businesses with the right investors.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button class="px-8 py-4 text-lg bg-blue-600 hover:bg-blue-700 text-white rounded-full theme-v2:bg-black theme-v2:hover:bg-gray-800">
                            Evaluate My Company →
                        </button>
                        <button class="px-8 py-4 text-lg border border-white/30 text-white hover:bg-white/10 rounded-full theme-v2:border-gray-300 theme-v2:v2-text-primary theme-v2:hover:bg-gray-50">
                            View Deal Pipeline
                        </button>
                    </div>
                </div>

                <!-- Dashboard Preview -->
                <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-xl border border-slate-700/50 rounded-3xl p-8 shadow-2xl theme-v2:v2-card theme-v2:bg-white theme-v2:border-gray-200">
                    <div class="grid lg:grid-cols-4 gap-6">
                        <!-- Sidebar -->
                        <div class="lg:col-span-1 bg-slate-900/80 rounded-2xl p-6 border border-slate-700/30 theme-v2:bg-gray-50 theme-v2:border-gray-200">
                            <div class="flex items-center gap-3 mb-8">
                                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white font-bold text-sm">CF</span>
                                </div>
                                <span class="text-white font-semibold theme-v2:v2-text-primary">CFOx</span>
                            </div>
                            <nav class="space-y-2">
                                <div class="flex items-center gap-3 px-3 py-2 rounded-lg bg-blue-600/20 text-blue-400 theme-v2:bg-blue-50 theme-v2:text-blue-600">
                                    <span class="text-sm">📊</span>
                                    <span class="text-sm font-medium">Dashboard</span>
                                </div>
                                <div class="flex items-center gap-3 px-3 py-2 rounded-lg text-slate-400 hover:text-slate-300 theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary">
                                    <span class="text-sm">📈</span>
                                    <span class="text-sm font-medium">Financial Health</span>
                                </div>
                            </nav>
                        </div>
                        <!-- Main Content -->
                        <div class="lg:col-span-3 space-y-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h2 class="text-2xl font-bold text-white mb-1 theme-v2:v2-text-primary">Financial Health Dashboard</h2>
                                    <div class="flex items-center gap-4">
                                        <span class="text-blue-400 text-sm font-medium theme-v2:text-blue-600">TechCorp Ltd</span>
                                        <span class="text-slate-500 text-sm theme-v2:v2-text-secondary">Last updated: Today</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Theme Toggle Button -->
        <div class="fixed bottom-6 right-6 z-50">
            <div class="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-4 shadow-lg theme-v2:bg-white theme-v2:border-gray-200 theme-v1:bg-slate-800/90 theme-v1:border-slate-700">
                <div class="flex items-center gap-3 mb-3">
                    <span class="text-sm font-medium text-gray-700 theme-v1:text-slate-200">UI Version</span>
                </div>
                <button id="themeToggle" class="w-full px-4 py-2 text-sm bg-black text-white rounded-lg hover:bg-gray-800 theme-v1:bg-white theme-v1:text-black theme-v1:hover:bg-gray-100">
                    Switch to V2 Light
                </button>
                <p class="text-xs text-gray-500 theme-v1:text-slate-400 mt-2 text-center" id="themeStatus">
                    Current: V1 Dark Theme
                </p>
            </div>
        </div>
    </div>

    <script>
        let currentTheme = 'v1';
        const toggleButton = document.getElementById('themeToggle');
        const themeStatus = document.getElementById('themeStatus');
        const body = document.body;

        function updateTheme() {
            body.classList.remove('theme-v1', 'theme-v2');
            body.classList.add(`theme-${currentTheme}`);
            
            if (currentTheme === 'v1') {
                toggleButton.textContent = 'Switch to V2 Light';
                themeStatus.textContent = 'Current: V1 Dark Theme';
            } else {
                toggleButton.textContent = 'Switch to V1 Dark';
                themeStatus.textContent = 'New: Clean & Professional';
            }
        }

        toggleButton.addEventListener('click', () => {
            currentTheme = currentTheme === 'v1' ? 'v2' : 'v1';
            updateTheme();
        });

        // Initialize
        updateTheme();
    </script>
</body>
</html>
