"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import ThemeToggle from "@/components/ui/theme-toggle";
import { useThemeStore } from "@/stores/themeStore";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { useEffect } from "react";

export default function Home() {
  const { version, setTheme } = useThemeStore();

  // Initialize theme on component mount
  useEffect(() => {
    setTheme(version);
  }, [version, setTheme]);
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 theme-v2:bg-white theme-v2:v2-gradient-bg">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50 theme-v2:v2-header theme-v2:bg-white/90 theme-v2:border-gray-200">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <span className="text-xl font-bold text-white theme-v2:v2-text-primary">CFOx</span>
          </div>
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/sme" className="text-slate-300 hover:text-white transition-colors theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary">For SMEs</Link>
            <Link href="/investor" className="text-slate-300 hover:text-white transition-colors theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary">For Investors</Link>
            <Link href="/consultant" className="text-slate-300 hover:text-white transition-colors theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary">For Consultants</Link>
            <Link href="/10x-growth-hack" className="text-slate-300 hover:text-white transition-colors theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary">10X Growth</Link>
            <Link href="/pricing" className="text-slate-300 hover:text-white transition-colors theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary">Pricing</Link>
            <Link href="/auth/signin" className="text-slate-300 hover:text-white transition-colors theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary">Log in</Link>
            <Link href="/auth/signup">
              <Button className="bg-white text-slate-900 hover:bg-slate-100 rounded-full px-6 theme-v2:bg-black theme-v2:text-white theme-v2:hover:bg-gray-800">
                Sign up
              </Button>
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-emerald-600/20" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-900/20 via-transparent to-transparent" />

        <div className="relative max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge variant="secondary" className="mb-6 px-6 py-2 text-sm font-medium bg-blue-500/10 text-blue-300 border-blue-500/20 theme-v2:bg-blue-50 theme-v2:text-blue-600 theme-v2:border-blue-200">
                🚀 10X Growth Hack Program Available
              </Badge>
              <h1 className="text-5xl sm:text-7xl font-bold text-white mb-8 leading-tight theme-v2:v2-text-primary">
                Are you
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-emerald-400 theme-v2:text-blue-600 theme-v2:bg-none">
                  10x growth ready?
                </span>
              </h1>
              <p className="text-xl text-slate-300 mb-12 max-w-2xl mx-auto leading-relaxed theme-v2:v2-text-secondary">
                Data-driven financial evaluation platform that automates scoring, provides actionable insights, and connects businesses with the right investors.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/sme">
                  <Button size="lg" className="px-8 py-4 text-lg bg-blue-600 hover:bg-blue-700 text-white rounded-full theme-v2:bg-black theme-v2:hover:bg-gray-800">
                    Evaluate My Company
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/investor">
                  <Button variant="outline" size="lg" className="px-8 py-4 text-lg border-white/30 text-white hover:bg-white/10 rounded-full theme-v2:border-gray-300 theme-v2:v2-text-primary theme-v2:hover:bg-gray-50">
                    View Deal Pipeline
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>

          {/* SME Dashboard Preview */}
          <motion.div
            className="relative max-w-6xl mx-auto"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-xl border border-slate-700/50 rounded-3xl p-8 shadow-2xl theme-v2:v2-card theme-v2:bg-white theme-v2:border-gray-200">
              <div className="grid lg:grid-cols-4 gap-6">
                {/* Sidebar */}
                <div className="lg:col-span-1 bg-slate-900/80 rounded-2xl p-6 border border-slate-700/30 theme-v2:bg-gray-50 theme-v2:border-gray-200">
                  <div className="flex items-center gap-3 mb-8">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">CF</span>
                    </div>
                    <span className="text-white font-semibold theme-v2:v2-text-primary">CFOx</span>
                  </div>

                  <nav className="space-y-2">
                    {[
                      { icon: "📊", label: "Dashboard", active: true },
                      { icon: "📈", label: "Financial Health" },
                      { icon: "🎯", label: "Investor Match" },
                      { icon: "📋", label: "Reports" },
                      { icon: "🚀", label: "10X Growth" }
                    ].map((item, index) => (
                      <div key={index} className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
                        item.active ? 'bg-blue-600/20 text-blue-400 theme-v2:bg-blue-50 theme-v2:text-blue-600' : 'text-slate-400 hover:text-slate-300 theme-v2:v2-text-secondary theme-v2:hover:v2-text-primary'
                      }`}>
                        <span className="text-sm">{item.icon}</span>
                        <span className="text-sm font-medium">{item.label}</span>
                      </div>
                    ))}
                  </nav>

                  <div className="mt-8 pt-6 border-t border-slate-700/50">
                    <div className="flex items-center gap-2 text-slate-400">
                      <div className="w-6 h-6 bg-slate-600 rounded-full"></div>
                      <span className="text-sm">TechCorp Ltd</span>
                    </div>
                  </div>
                </div>
                {/* Main Content */}
                <div className="lg:col-span-3 space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-2xl font-bold text-white mb-1">Financial Health Dashboard</h2>
                      <div className="flex items-center gap-4">
                        <span className="text-blue-400 text-sm font-medium">TechCorp Ltd</span>
                        <span className="text-slate-500 text-sm">Last updated: Today</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="w-5 h-5 text-slate-400">🔔</div>
                      <div className="w-8 h-8 bg-slate-600 rounded-full"></div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Financial Health Score */}
                    <Card className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border-slate-700/50">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-slate-400 text-sm">Financial Health Score</span>
                          <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                        </div>
                        <div className="text-3xl font-bold text-emerald-400 mb-2">78/100</div>
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-slate-400">Grade: B+</span>
                          <span className="text-slate-500">•</span>
                          <span className="text-emerald-400">+5 this month</span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Investor Interest */}
                    <Card className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border-slate-700/50">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-slate-400 text-sm">Investor Interest</span>
                          <select className="bg-slate-700/50 text-slate-300 text-xs px-2 py-1 rounded border-slate-600">
                            <option>This Month</option>
                          </select>
                        </div>
                        <div className="relative">
                          <div className="w-24 h-24 mx-auto">
                            <div className="w-full h-full rounded-full border-8 border-slate-700 relative">
                              <div className="absolute inset-0 rounded-full border-8 border-blue-400 border-t-transparent transform rotate-45"></div>
                            </div>
                          </div>
                          <div className="text-center mt-4">
                            <div className="flex items-center justify-center gap-4 text-xs">
                              <div className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                <span className="text-slate-400">High Interest</span>
                                <span className="text-white">12</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-slate-600 rounded-full"></div>
                                <span className="text-slate-400">Watching</span>
                                <span className="text-white">28</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Key Metrics Overview */}
                  <Card className="bg-gradient-to-br from-slate-700/30 to-slate-800/30 border-slate-600/30">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-6">
                        <span className="text-slate-300 text-sm font-medium">Key Financial Metrics</span>
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📊</span>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="text-lg font-bold text-emerald-400">$3.1M</div>
                          <div className="text-xs text-slate-400">Annual Revenue</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-blue-400">18%</div>
                          <div className="text-xs text-slate-400">Profit Margin</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-amber-400">1.8x</div>
                          <div className="text-xs text-slate-400">Debt-to-Equity</div>
                        </div>
                      </div>
                      <div className="mt-4 pt-4 border-t border-slate-600">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-400">10X Growth Eligible</span>
                          <span className="text-emerald-400 font-medium">✓ Qualified</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Platform Statistics */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-400 mb-2">500+</div>
              <div className="text-slate-300">SMEs Evaluated</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-emerald-400 mb-2">$65M+</div>
              <div className="text-slate-300">Funding Facilitated</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-amber-400 mb-2">95%</div>
              <div className="text-slate-300">Accuracy Rate</div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Role Selection Section */}
      <section className="py-20 px-4 bg-slate-800/50">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">Choose Your Path</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              Select your role to access tailored features and insights designed for your specific needs.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* SME Owner */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-blue-500/50 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-blue-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span className="text-3xl">📈</span>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">SME Owner</h3>
                  <p className="text-slate-300 mb-6">
                    Get your business evaluated and connect with investors through our data-driven platform.
                  </p>
                  <ul className="space-y-2 text-sm text-slate-400">
                    <li>• Automated Financial Health Score</li>
                    <li>• Secure Document Upload</li>
                    <li>• Investor Matching</li>
                  </ul>
                  <Link href="/sme">
                    <Button className="w-full mt-6 bg-blue-600 hover:bg-blue-700">
                      Get Started <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>

            {/* Investor */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-emerald-500/50 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-emerald-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span className="text-3xl">💰</span>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">Investor</h3>
                  <p className="text-slate-300 mb-6">
                    Discover and evaluate investment opportunities with comprehensive risk assessment tools.
                  </p>
                  <ul className="space-y-2 text-sm text-slate-400">
                    <li>• Curated Deal Pipeline</li>
                    <li>• Risk Assessment Tools</li>
                    <li>• Performance Analytics</li>
                  </ul>
                  <Link href="/investor">
                    <Button className="w-full mt-6 bg-emerald-600 hover:bg-emerald-700">
                      Explore Deals <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>

            {/* Consultant */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-amber-500/50 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-amber-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span className="text-3xl">🎯</span>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">Consultant</h3>
                  <p className="text-slate-300 mb-6">
                    Help SMEs improve their financial health and earn referral rewards through our platform.
                  </p>
                  <ul className="space-y-2 text-sm text-slate-400">
                    <li>• Client Management Tools</li>
                    <li>• Referral Tracking</li>
                    <li>• Performance Insights</li>
                  </ul>
                  <Link href="/consultant">
                    <Button className="w-full mt-6 bg-amber-600 hover:bg-amber-700">
                      Start Consulting <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* 10X Growth Hack CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-emerald-600">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🎯 Exclusive Program
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">10X Growth Hack Program</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join our exclusive program and accelerate your business growth with personalized strategies and expert guidance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/10x-growth-hack">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-4 rounded-full">
                  Learn More
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/pricing">
                <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10 text-lg px-8 py-4 rounded-full">
                  View Pricing
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-800 py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">CF</span>
                </div>
                <span className="text-xl font-bold text-white">CFOx</span>
              </div>
              <p className="text-slate-400 mb-4">
                Connecting SMEs with smart investors through data-driven insights and automated financial scoring.
              </p>
              <div className="flex space-x-4">
                <div className="w-8 h-8 bg-slate-800 rounded-full flex items-center justify-center hover:bg-slate-700 cursor-pointer">
                  <span className="text-slate-400 text-sm">f</span>
                </div>
                <div className="w-8 h-8 bg-slate-800 rounded-full flex items-center justify-center hover:bg-slate-700 cursor-pointer">
                  <span className="text-slate-400 text-sm">t</span>
                </div>
                <div className="w-8 h-8 bg-slate-800 rounded-full flex items-center justify-center hover:bg-slate-700 cursor-pointer">
                  <span className="text-slate-400 text-sm">in</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Platform</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="/sme" className="hover:text-white transition-colors">For SMEs</a></li>
                <li><a href="/investor" className="hover:text-white transition-colors">For Investors</a></li>
                <li><a href="/consultant" className="hover:text-white transition-colors">For Consultants</a></li>
                <li><a href="/10x-growth-hack" className="hover:text-white transition-colors">10X Growth</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Resources</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="/pricing" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Case Studies</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Company</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-slate-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">&copy; 2024 CFOx. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Terms of Service</a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
            </div>
          </div>
        </div>
      </footer>

      {/* Theme Toggle */}
      <ThemeToggle />
    </div>
  );
}