@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: Inter, system-ui, sans-serif;
  --font-mono: JetBrains Mono, monospace;

  /* Premium Dark Theme Colors */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-900: #0c4a6e;

  /* Sophisticated Green Accents */
  --color-secondary-50: #f0fdf4;
  --color-secondary-100: #dcfce7;
  --color-secondary-500: #22c55e;
  --color-secondary-600: #16a34a;
  --color-secondary-700: #15803d;

  /* Premium Gold Accents */
  --color-accent-50: #fffbeb;
  --color-accent-100: #fef3c7;
  --color-accent-500: #f59e0b;
  --color-accent-600: #d97706;
  --color-accent-700: #b45309;

  /* Dark Theme Colors */
  --color-dark-50: #f8fafc;
  --color-dark-100: #f1f5f9;
  --color-dark-200: #e2e8f0;
  --color-dark-300: #cbd5e1;
  --color-dark-400: #94a3b8;
  --color-dark-500: #64748b;
  --color-dark-600: #475569;
  --color-dark-700: #334155;
  --color-dark-800: #1e293b;
  --color-dark-900: #0f172a;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --primary: #0284c7;
  --primary-foreground: #ffffff;
  --secondary: #334155;
  --secondary-foreground: #f8fafc;
  --muted: #475569;
  --muted-foreground: #94a3b8;
  --accent: #334155;
  --accent-foreground: #f8fafc;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #334155;
  --ring: #0284c7;
  --chart-1: #0ea5e9;
  --chart-2: #22c55e;
  --chart-3: #f59e0b;
  --chart-4: #8b5cf6;
  --chart-5: #ef4444;
  --sidebar: #0f172a;
  --sidebar-foreground: #f8fafc;
  --sidebar-primary: #0284c7;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #1e293b;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: #334155;
  --sidebar-ring: #0284c7;
}

.light {
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #0284c7;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f8fafc;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #0284c7;
  --chart-1: #0ea5e9;
  --chart-2: #22c55e;
  --chart-3: #f59e0b;
  --chart-4: #8b5cf6;
  --chart-5: #ef4444;
  --sidebar: #0f172a;
  --sidebar-foreground: #f8fafc;
  --sidebar-primary: #0284c7;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #1e293b;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: #334155;
  --sidebar-ring: #0284c7;
}

/* V2 Theme Overrides - High Specificity */
body.theme-v2 {
  background: #ffffff !important;
  color: #000000 !important;
}

/* V2 Background Overrides */
body.theme-v2 .bg-gradient-to-br {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
}

body.theme-v2 .from-slate-900,
body.theme-v2 .via-slate-800,
body.theme-v2 .to-slate-900 {
  background: #ffffff !important;
}

/* V2 Header Overrides */
body.theme-v2 header {
  background: rgba(255, 255, 255, 0.9) !important;
  border-bottom: 1px solid #e5e7eb !important;
  backdrop-filter: blur(8px) !important;
}

body.theme-v2 .border-slate-700\/50 {
  border-color: #e5e7eb !important;
}

body.theme-v2 .bg-slate-900\/80 {
  background: rgba(255, 255, 255, 0.9) !important;
}

/* V2 Text Overrides */
body.theme-v2 .text-white {
  color: #000000 !important;
}

body.theme-v2 .text-slate-300 {
  color: #6b7280 !important;
}

body.theme-v2 .text-slate-400 {
  color: #9ca3af !important;
}

body.theme-v2 .text-slate-500 {
  color: #6b7280 !important;
}

/* V2 Button Overrides */
body.theme-v2 .bg-white {
  background: #000000 !important;
  color: #ffffff !important;
}

body.theme-v2 .hover\:bg-slate-100:hover {
  background: #374151 !important;
}

body.theme-v2 .text-slate-900 {
  color: #ffffff !important;
}

/* V2 Card Overrides */
body.theme-v2 .bg-gradient-to-br.from-slate-800\/50 {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

body.theme-v2 .bg-slate-900\/80 {
  background: #f9fafb !important;
  border-color: #e5e7eb !important;
}

body.theme-v2 .border-slate-700\/30,
body.theme-v2 .border-slate-700\/50 {
  border-color: #e5e7eb !important;
}

/* V2 Badge Overrides */
body.theme-v2 .bg-blue-500\/10 {
  background: #dbeafe !important;
}

body.theme-v2 .text-blue-300 {
  color: #2563eb !important;
}

body.theme-v2 .border-blue-500\/20 {
  border-color: #93c5fd !important;
}

/* V2 Gradient Text Override */
body.theme-v2 .bg-clip-text.text-transparent {
  color: #2563eb !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  background-clip: unset !important;
}

/* V2 Button Variants */
body.theme-v2 .bg-blue-600 {
  background: #000000 !important;
}

body.theme-v2 .hover\:bg-blue-700:hover {
  background: #374151 !important;
}

body.theme-v2 .border-white\/30 {
  border-color: #d1d5db !important;
}

body.theme-v2 .hover\:bg-white\/10:hover {
  background: #f3f4f6 !important;
  color: #000000 !important;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}
