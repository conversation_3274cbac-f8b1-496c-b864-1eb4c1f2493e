<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CFOx V2 Theme Test - Simple</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* V2 Theme Overrides - High Specificity */
        body.theme-v2 {
            background: #ffffff !important;
            color: #000000 !important;
        }

        body.theme-v2 .bg-gradient-to-br {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
        }

        body.theme-v2 .text-white {
            color: #000000 !important;
        }

        body.theme-v2 .text-slate-300 {
            color: #6b7280 !important;
        }

        body.theme-v2 .bg-white {
            background: #000000 !important;
            color: #ffffff !important;
        }

        body.theme-v2 .text-slate-900 {
            color: #ffffff !important;
        }

        body.theme-v2 header {
            background: rgba(255, 255, 255, 0.9) !important;
            border-bottom: 1px solid #e5e7eb !important;
        }

        body.theme-v2 .bg-slate-900\/80 {
            background: rgba(255, 255, 255, 0.9) !important;
        }

        body.theme-v2 .border-slate-700\/50 {
            border-color: #e5e7eb !important;
        }

        body {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="theme-v1">
    <div class="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <!-- Header -->
        <header class="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
            <div class="container mx-auto px-4 py-4 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">CF</span>
                    </div>
                    <span class="text-xl font-bold text-white">CFOx</span>
                </div>
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-slate-300 hover:text-white transition-colors">For SMEs</a>
                    <a href="#" class="text-slate-300 hover:text-white transition-colors">For Investors</a>
                    <button class="bg-white text-slate-900 hover:bg-slate-100 rounded-full px-6 py-2">
                        Sign up
                    </button>
                </nav>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="relative overflow-hidden py-20 px-4">
            <div class="relative max-w-7xl mx-auto text-center">
                <h1 class="text-5xl font-bold text-white mb-8 leading-tight">
                    Are you
                    <span class="block text-blue-400">
                        10x growth ready?
                    </span>
                </h1>
                <p class="text-xl text-slate-300 mb-12 max-w-2xl mx-auto">
                    Data-driven financial evaluation platform that automates scoring and connects businesses with investors.
                </p>
                <div class="flex gap-4 justify-center">
                    <button class="px-8 py-4 text-lg bg-blue-600 hover:bg-blue-700 text-white rounded-full">
                        Get Started
                    </button>
                    <button class="px-8 py-4 text-lg border border-white/30 text-white hover:bg-white/10 rounded-full">
                        Learn More
                    </button>
                </div>
            </div>
        </section>

        <!-- Theme Toggle Button -->
        <div class="fixed bottom-6 right-6 z-50">
            <div class="bg-slate-800/90 border border-slate-700 rounded-2xl p-4 shadow-lg">
                <div class="mb-3">
                    <span class="text-sm font-medium text-slate-200">UI Version</span>
                </div>
                <div class="mb-3">
                    <span id="currentTheme" class="text-xs bg-slate-700 text-slate-200 px-2 py-1 rounded">V1 Dark</span>
                </div>
                <button id="themeToggle" class="w-full px-4 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                    Switch to V2 Light
                </button>
                <p id="themeStatus" class="text-xs text-slate-400 mt-2 text-center">
                    Current: Dark Theme
                </p>
            </div>
        </div>
    </div>

    <script>
        let currentTheme = 'v1';
        const toggleButton = document.getElementById('themeToggle');
        const themeStatus = document.getElementById('themeStatus');
        const currentThemeSpan = document.getElementById('currentTheme');
        const body = document.body;

        function updateTheme() {
            console.log('Updating theme to:', currentTheme);
            
            // Remove existing theme classes
            body.classList.remove('theme-v1', 'theme-v2');
            
            // Add new theme class
            body.classList.add(`theme-${currentTheme}`);
            
            console.log('Body classes after update:', body.className);
            
            // Update UI text
            if (currentTheme === 'v1') {
                toggleButton.textContent = 'Switch to V2 Light';
                themeStatus.textContent = 'Current: Dark Theme';
                currentThemeSpan.textContent = 'V1 Dark';
            } else {
                toggleButton.textContent = 'Switch to V1 Dark';
                themeStatus.textContent = 'New: Clean & Professional';
                currentThemeSpan.textContent = 'V2 Light';
            }
        }

        toggleButton.addEventListener('click', () => {
            console.log('Toggle clicked, current theme:', currentTheme);
            currentTheme = currentTheme === 'v1' ? 'v2' : 'v1';
            console.log('New theme will be:', currentTheme);
            updateTheme();
        });

        // Initialize
        updateTheme();
        
        console.log('Theme test initialized');
    </script>
</body>
</html>
